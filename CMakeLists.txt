cmake_minimum_required(VERSION 4.0)
project(BaseWidget)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)


find_package(Qt5 COMPONENTS
        Core
        Gui
        Widgets
        REQUIRED)

# 通知组件源文件
set(NOTIFICATION_SOURCES
    src/componets/notification/NotificationItem.cpp
    src/componets/notification/NotificationItemWidget.cpp
    src/componets/notification/NotificationListWidget.cpp
    src/componets/notification/NotificationWindow.cpp
)

# 通知组件头文件
set(NOTIFICATION_HEADERS
    src/componets/notification/NotificationItem.h
    src/componets/notification/NotificationItemWidget.h
    src/componets/notification/NotificationListWidget.h
    src/componets/notification/NotificationWindow.h
)

add_executable(BaseWidget
    main.cpp
    ${NOTIFICATION_SOURCES}
    ${NOTIFICATION_HEADERS}
)

target_link_libraries(BaseWidget
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
)

# 包含头文件目录
target_include_directories(BaseWidget PRIVATE
    src/componets/notification
)

