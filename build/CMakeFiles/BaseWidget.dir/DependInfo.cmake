
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/Users/<USER>/CLionProjects/BaseWidget/build/BaseWidget_autogen/mocs_compilation.cpp" "CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o" "gcc" "CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/main.cpp" "CMakeFiles/BaseWidget.dir/main.cpp.o" "gcc" "CMakeFiles/BaseWidget.dir/main.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItem.cpp" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o" "gcc" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItemWidget.cpp" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o" "gcc" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationListWidget.cpp" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o" "gcc" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o.d"
  "/Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationWindow.cpp" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o" "gcc" "CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
