# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/build

# Include any dependencies generated for this target.
include CMakeFiles/BaseWidget.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/BaseWidget.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/BaseWidget.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/BaseWidget.dir/flags.make

CMakeFiles/BaseWidget.dir/codegen:
.PHONY : CMakeFiles/BaseWidget.dir/codegen

CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o: CMakeFiles/BaseWidget.dir/flags.make
CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o: BaseWidget_autogen/mocs_compilation.cpp
CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o: CMakeFiles/BaseWidget.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o -MF CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/build/BaseWidget_autogen/mocs_compilation.cpp

CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/build/BaseWidget_autogen/mocs_compilation.cpp > CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.i

CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/build/BaseWidget_autogen/mocs_compilation.cpp -o CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.s

CMakeFiles/BaseWidget.dir/main.cpp.o: CMakeFiles/BaseWidget.dir/flags.make
CMakeFiles/BaseWidget.dir/main.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/main.cpp
CMakeFiles/BaseWidget.dir/main.cpp.o: CMakeFiles/BaseWidget.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/BaseWidget.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BaseWidget.dir/main.cpp.o -MF CMakeFiles/BaseWidget.dir/main.cpp.o.d -o CMakeFiles/BaseWidget.dir/main.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/main.cpp

CMakeFiles/BaseWidget.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BaseWidget.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/main.cpp > CMakeFiles/BaseWidget.dir/main.cpp.i

CMakeFiles/BaseWidget.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BaseWidget.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/main.cpp -o CMakeFiles/BaseWidget.dir/main.cpp.s

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o: CMakeFiles/BaseWidget.dir/flags.make
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItem.cpp
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o: CMakeFiles/BaseWidget.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o -MF CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o.d -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItem.cpp

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItem.cpp > CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.i

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItem.cpp -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.s

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o: CMakeFiles/BaseWidget.dir/flags.make
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItemWidget.cpp
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o: CMakeFiles/BaseWidget.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o -MF CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o.d -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItemWidget.cpp

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItemWidget.cpp > CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.i

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationItemWidget.cpp -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.s

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o: CMakeFiles/BaseWidget.dir/flags.make
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationListWidget.cpp
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o: CMakeFiles/BaseWidget.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o -MF CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o.d -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationListWidget.cpp

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationListWidget.cpp > CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.i

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationListWidget.cpp -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.s

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o: CMakeFiles/BaseWidget.dir/flags.make
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o: /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationWindow.cpp
CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o: CMakeFiles/BaseWidget.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o -MF CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o.d -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o -c /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationWindow.cpp

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationWindow.cpp > CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.i

CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/BaseWidget/src/componets/notification/NotificationWindow.cpp -o CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.s

# Object files for target BaseWidget
BaseWidget_OBJECTS = \
"CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/BaseWidget.dir/main.cpp.o" \
"CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o" \
"CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o" \
"CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o" \
"CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o"

# External object files for target BaseWidget
BaseWidget_EXTERNAL_OBJECTS =

BaseWidget: CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o
BaseWidget: CMakeFiles/BaseWidget.dir/main.cpp.o
BaseWidget: CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o
BaseWidget: CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o
BaseWidget: CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o
BaseWidget: CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o
BaseWidget: CMakeFiles/BaseWidget.dir/build.make
BaseWidget: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtWidgets.framework/QtWidgets
BaseWidget: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtGui.framework/QtGui
BaseWidget: /opt/homebrew/Cellar/qt@5/5.15.17/lib/QtCore.framework/QtCore
BaseWidget: CMakeFiles/BaseWidget.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX executable BaseWidget"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/BaseWidget.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/BaseWidget.dir/build: BaseWidget
.PHONY : CMakeFiles/BaseWidget.dir/build

CMakeFiles/BaseWidget.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/BaseWidget.dir/cmake_clean.cmake
.PHONY : CMakeFiles/BaseWidget.dir/clean

CMakeFiles/BaseWidget.dir/depend:
	cd /Users/<USER>/CLionProjects/BaseWidget/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles/BaseWidget.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/BaseWidget.dir/depend

