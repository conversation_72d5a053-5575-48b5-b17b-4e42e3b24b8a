# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/BaseWidget

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/BaseWidget/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles /Users/<USER>/CLionProjects/BaseWidget/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : all

# The main codegen target
codegen: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles /Users/<USER>/CLionProjects/BaseWidget/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 codegen
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/BaseWidget/build/CMakeFiles 0
.PHONY : codegen

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named BaseWidget

# Build rule for target.
BaseWidget: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 BaseWidget
.PHONY : BaseWidget

# fast build rule for target.
BaseWidget/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/build
.PHONY : BaseWidget/fast

#=============================================================================
# Target rules for targets named BaseWidget_autogen_timestamp_deps

# Build rule for target.
BaseWidget_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 BaseWidget_autogen_timestamp_deps
.PHONY : BaseWidget_autogen_timestamp_deps

# fast build rule for target.
BaseWidget_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build.make CMakeFiles/BaseWidget_autogen_timestamp_deps.dir/build
.PHONY : BaseWidget_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named BaseWidget_autogen

# Build rule for target.
BaseWidget_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 BaseWidget_autogen
.PHONY : BaseWidget_autogen

# fast build rule for target.
BaseWidget_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget_autogen.dir/build.make CMakeFiles/BaseWidget_autogen.dir/build
.PHONY : BaseWidget_autogen/fast

BaseWidget_autogen/mocs_compilation.o: BaseWidget_autogen/mocs_compilation.cpp.o
.PHONY : BaseWidget_autogen/mocs_compilation.o

# target to build an object file
BaseWidget_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.o
.PHONY : BaseWidget_autogen/mocs_compilation.cpp.o

BaseWidget_autogen/mocs_compilation.i: BaseWidget_autogen/mocs_compilation.cpp.i
.PHONY : BaseWidget_autogen/mocs_compilation.i

# target to preprocess a source file
BaseWidget_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.i
.PHONY : BaseWidget_autogen/mocs_compilation.cpp.i

BaseWidget_autogen/mocs_compilation.s: BaseWidget_autogen/mocs_compilation.cpp.s
.PHONY : BaseWidget_autogen/mocs_compilation.s

# target to generate assembly for a file
BaseWidget_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/BaseWidget_autogen/mocs_compilation.cpp.s
.PHONY : BaseWidget_autogen/mocs_compilation.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/main.cpp.s
.PHONY : main.cpp.s

src/componets/notification/NotificationItem.o: src/componets/notification/NotificationItem.cpp.o
.PHONY : src/componets/notification/NotificationItem.o

# target to build an object file
src/componets/notification/NotificationItem.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.o
.PHONY : src/componets/notification/NotificationItem.cpp.o

src/componets/notification/NotificationItem.i: src/componets/notification/NotificationItem.cpp.i
.PHONY : src/componets/notification/NotificationItem.i

# target to preprocess a source file
src/componets/notification/NotificationItem.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.i
.PHONY : src/componets/notification/NotificationItem.cpp.i

src/componets/notification/NotificationItem.s: src/componets/notification/NotificationItem.cpp.s
.PHONY : src/componets/notification/NotificationItem.s

# target to generate assembly for a file
src/componets/notification/NotificationItem.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItem.cpp.s
.PHONY : src/componets/notification/NotificationItem.cpp.s

src/componets/notification/NotificationItemWidget.o: src/componets/notification/NotificationItemWidget.cpp.o
.PHONY : src/componets/notification/NotificationItemWidget.o

# target to build an object file
src/componets/notification/NotificationItemWidget.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.o
.PHONY : src/componets/notification/NotificationItemWidget.cpp.o

src/componets/notification/NotificationItemWidget.i: src/componets/notification/NotificationItemWidget.cpp.i
.PHONY : src/componets/notification/NotificationItemWidget.i

# target to preprocess a source file
src/componets/notification/NotificationItemWidget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.i
.PHONY : src/componets/notification/NotificationItemWidget.cpp.i

src/componets/notification/NotificationItemWidget.s: src/componets/notification/NotificationItemWidget.cpp.s
.PHONY : src/componets/notification/NotificationItemWidget.s

# target to generate assembly for a file
src/componets/notification/NotificationItemWidget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationItemWidget.cpp.s
.PHONY : src/componets/notification/NotificationItemWidget.cpp.s

src/componets/notification/NotificationListWidget.o: src/componets/notification/NotificationListWidget.cpp.o
.PHONY : src/componets/notification/NotificationListWidget.o

# target to build an object file
src/componets/notification/NotificationListWidget.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.o
.PHONY : src/componets/notification/NotificationListWidget.cpp.o

src/componets/notification/NotificationListWidget.i: src/componets/notification/NotificationListWidget.cpp.i
.PHONY : src/componets/notification/NotificationListWidget.i

# target to preprocess a source file
src/componets/notification/NotificationListWidget.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.i
.PHONY : src/componets/notification/NotificationListWidget.cpp.i

src/componets/notification/NotificationListWidget.s: src/componets/notification/NotificationListWidget.cpp.s
.PHONY : src/componets/notification/NotificationListWidget.s

# target to generate assembly for a file
src/componets/notification/NotificationListWidget.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationListWidget.cpp.s
.PHONY : src/componets/notification/NotificationListWidget.cpp.s

src/componets/notification/NotificationWindow.o: src/componets/notification/NotificationWindow.cpp.o
.PHONY : src/componets/notification/NotificationWindow.o

# target to build an object file
src/componets/notification/NotificationWindow.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.o
.PHONY : src/componets/notification/NotificationWindow.cpp.o

src/componets/notification/NotificationWindow.i: src/componets/notification/NotificationWindow.cpp.i
.PHONY : src/componets/notification/NotificationWindow.i

# target to preprocess a source file
src/componets/notification/NotificationWindow.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.i
.PHONY : src/componets/notification/NotificationWindow.cpp.i

src/componets/notification/NotificationWindow.s: src/componets/notification/NotificationWindow.cpp.s
.PHONY : src/componets/notification/NotificationWindow.s

# target to generate assembly for a file
src/componets/notification/NotificationWindow.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/BaseWidget.dir/build.make CMakeFiles/BaseWidget.dir/src/componets/notification/NotificationWindow.cpp.s
.PHONY : src/componets/notification/NotificationWindow.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... codegen"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... BaseWidget_autogen"
	@echo "... BaseWidget_autogen_timestamp_deps"
	@echo "... BaseWidget"
	@echo "... BaseWidget_autogen/mocs_compilation.o"
	@echo "... BaseWidget_autogen/mocs_compilation.i"
	@echo "... BaseWidget_autogen/mocs_compilation.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... src/componets/notification/NotificationItem.o"
	@echo "... src/componets/notification/NotificationItem.i"
	@echo "... src/componets/notification/NotificationItem.s"
	@echo "... src/componets/notification/NotificationItemWidget.o"
	@echo "... src/componets/notification/NotificationItemWidget.i"
	@echo "... src/componets/notification/NotificationItemWidget.s"
	@echo "... src/componets/notification/NotificationListWidget.o"
	@echo "... src/componets/notification/NotificationListWidget.i"
	@echo "... src/componets/notification/NotificationListWidget.s"
	@echo "... src/componets/notification/NotificationWindow.o"
	@echo "... src/componets/notification/NotificationWindow.i"
	@echo "... src/componets/notification/NotificationWindow.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

