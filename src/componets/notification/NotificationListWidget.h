#ifndef NOTIFICATIONLISTWIDGET_H
#define NOTIFICATIONLISTWIDGET_H

#include <QWidget>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QLabel>
#include <QList>
#include <QTimer>
#include "NotificationItem.h"
#include "NotificationItemWidget.h"

/**
 * @brief 通知列表组件
 * 以时间线方式显示通知列表
 */
class NotificationListWidget : public QWidget
{
    Q_OBJECT

public:
    explicit NotificationListWidget(QWidget *parent = nullptr);
    
    // 添加通知
    void addNotification(const NotificationItem& item);
    
    // 移除通知
    void removeNotification(NotificationItemWidget* widget);
    
    // 清空所有通知
    void clearAllNotifications();
    
    // 获取通知数量
    int getNotificationCount() const;
    
    // 按状态过滤通知
    void filterByStatus(NotificationStatus status);
    
    // 显示所有通知
    void showAllNotifications();

signals:
    // 通知数量变化信号
    void notificationCountChanged(int count);

private slots:
    // 删除通知槽函数
    void onNotificationDeleteRequested(NotificationItemWidget* widget);
    
    // 更新时间显示
    void updateTimeDisplay();

private:
    void setupUI();
    void addTimelineLabel(const QString& timeText);
    void updateLayout();
    
    QScrollArea* m_scrollArea;
    QWidget* m_contentWidget;
    QVBoxLayout* m_contentLayout;
    
    QList<NotificationItemWidget*> m_notificationWidgets;
    QTimer* m_timeUpdateTimer;  // 定时更新时间显示
    
    NotificationStatus m_currentFilter;
    bool m_showAll;
};

#endif // NOTIFICATIONLISTWIDGET_H
