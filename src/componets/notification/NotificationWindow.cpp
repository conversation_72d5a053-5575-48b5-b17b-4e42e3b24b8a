#include "NotificationWindow.h"
#include <QApplication>
#include <QStyle>
#include <QRandomGenerator>
#include <QToolBar>
#include <QStatusBar>

NotificationWindow::NotificationWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_toolBar(nullptr)
    , m_clearAllAction(nullptr)
    , m_addTestAction(nullptr)
    , m_filterComboBox(nullptr)
    , m_notificationList(nullptr)
    , m_statusBar(nullptr)
    , m_countLabel(nullptr)
{
    setupUI();
    setupToolBar();
    setupStatusBar();
    
    // 设置窗口属性
    setWindowTitle("通知中心");
    setMinimumSize(600, 400);
    resize(800, 600);
    
    // 添加一些测试通知
    addTestNotifications();
}

void NotificationWindow::setupUI()
{
    // 创建中央组件
    m_centralWidget = new QWidget();
    setCentralWidget(m_centralWidget);
    
    // 创建主布局
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    // 创建通知列表
    m_notificationList = new NotificationListWidget();
    
    // 连接信号
    connect(m_notificationList, &NotificationListWidget::notificationCountChanged,
            this, &NotificationWindow::onNotificationCountChanged);
    
    // 添加到布局
    m_mainLayout->addWidget(m_notificationList);
}

void NotificationWindow::setupToolBar()
{
    m_toolBar = addToolBar("主工具栏");
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    
    // 过滤下拉框
    m_filterComboBox = new QComboBox();
    m_filterComboBox->addItem("全部通知", -1);
    m_filterComboBox->addItem("普通", static_cast<int>(NotificationStatus::Normal));
    m_filterComboBox->addItem("警告", static_cast<int>(NotificationStatus::Warning));
    m_filterComboBox->addItem("错误", static_cast<int>(NotificationStatus::Error));
    
    connect(m_filterComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &NotificationWindow::onFilterChanged);
    
    m_toolBar->addWidget(new QLabel("过滤: "));
    m_toolBar->addWidget(m_filterComboBox);
    m_toolBar->addSeparator();
    
    // 清空所有通知操作
    m_clearAllAction = new QAction("清空所有", this);
    m_clearAllAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_TrashIcon));
    m_clearAllAction->setToolTip("清空所有通知");
    connect(m_clearAllAction, &QAction::triggered, this, &NotificationWindow::onClearAllClicked);
    m_toolBar->addAction(m_clearAllAction);
    
    // 添加测试通知操作
    m_addTestAction = new QAction("添加测试通知", this);
    m_addTestAction->setIcon(QApplication::style()->standardIcon(QStyle::SP_FileIcon));
    m_addTestAction->setToolTip("添加一些测试通知");
    connect(m_addTestAction, &QAction::triggered, this, &NotificationWindow::onAddTestNotificationClicked);
    m_toolBar->addAction(m_addTestAction);
}

void NotificationWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    
    m_countLabel = new QLabel("通知数量: 0");
    m_statusBar->addPermanentWidget(m_countLabel);
}

void NotificationWindow::addNotification(const NotificationItem& item) const
{
    m_notificationList->addNotification(item);
}

void NotificationWindow::addTestNotifications() const
{
    // 添加一些测试通知
    createTestNotification("项目A", "构建成功", "项目A已成功构建完成，所有测试通过。", NotificationStatus::Normal);
    createTestNotification("项目B", "编译警告", "项目B编译时发现3个警告，建议检查代码。", NotificationStatus::Warning);
    createTestNotification("项目C", "构建失败", "项目C构建失败，请检查依赖项和配置。", NotificationStatus::Error);
    createTestNotification("系统", "内存使用率高", "当前系统内存使用率已达到85%，建议关闭不必要的程序。", NotificationStatus::Warning);
    createTestNotification("网络", "连接正常", "网络连接已恢复正常，所有服务可用。", NotificationStatus::Normal);
}

void NotificationWindow::createTestNotification(const QString& project, 
                                               const QString& title, 
                                               const QString& content,
                                               NotificationStatus status) const
{
    NotificationItem item(project, title, content, status, NotificationType::Silent);
    addNotification(item);
}

void NotificationWindow::onClearAllClicked() const
{
    m_notificationList->clearAllNotifications();
}

void NotificationWindow::onFilterChanged(int index) const
{
    int filterValue = m_filterComboBox->itemData(index).toInt();
    
    if (filterValue == -1) {
        // 显示所有通知
        m_notificationList->showAllNotifications();
    } else {
        // 按状态过滤
        auto status = static_cast<NotificationStatus>(filterValue);
        m_notificationList->filterByStatus(status);
    }
}

void NotificationWindow::onAddTestNotificationClicked() const
{
    // 随机添加一个测试通知
    QStringList projects = {"项目A", "项目B", "项目C", "系统", "网络"};
    QStringList titles = {"构建完成", "测试通过", "部署成功", "更新可用", "备份完成"};
    QStringList contents = {
        "所有任务已成功完成。",
        "检测到新的更新版本。",
        "系统运行正常。",
        "操作已成功执行。",
        "所有检查项目都通过了。"
    };
    
    QList<NotificationStatus> statuses = {
        NotificationStatus::Normal,
        NotificationStatus::Warning,
        NotificationStatus::Error
    };

    const int projectIndex = QRandomGenerator::global()->bounded(projects.size());
    const int titleIndex = QRandomGenerator::global()->bounded(titles.size());
    const int contentIndex = QRandomGenerator::global()->bounded(contents.size());
    const int statusIndex = QRandomGenerator::global()->bounded(statuses.size());
    
    createTestNotification(projects[projectIndex], 
                          titles[titleIndex], 
                          contents[contentIndex],
                          statuses[statusIndex]);
}

void NotificationWindow::onNotificationCountChanged(const int count) const
{
    m_countLabel->setText(QString("通知数量: %1").arg(count));
}
