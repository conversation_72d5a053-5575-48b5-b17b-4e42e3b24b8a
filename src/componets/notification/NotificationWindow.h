#ifndef NOTIFICATIONWINDOW_H
#define NOTIFICATIONWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QLabel>
#include <QComboBox>
#include "NotificationListWidget.h"
#include "NotificationItem.h"

/**
 * @brief 主通知窗口
 * 整合所有通知组件，提供完整的通知管理界面
 */
class NotificationWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit NotificationWindow(QWidget *parent = nullptr);
    
    // 添加通知
    void addNotification(const NotificationItem& item) const;
    
    // 添加测试通知（用于演示）
    void addTestNotifications() const;

private slots:
    // 工具栏操作槽函数
    void onClearAllClicked() const;
    void onFilterChanged(int index) const;
    void onAddTestNotificationClicked() const;
    
    // 通知数量变化槽函数
    void onNotificationCountChanged(int count) const;

private:
    void setupUI();
    void setupToolBar();
    void setupStatusBar();
    void createTestNotification(const QString& project, 
                               const QString& title, 
                               const QString& content,
                               NotificationStatus status) const;
    
    // UI组件
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    
    QToolBar* m_toolBar;
    QAction* m_clearAllAction;
    QAction* m_addTestAction;
    QComboBox* m_filterComboBox;
    
    NotificationListWidget* m_notificationList;
    
    QStatusBar* m_statusBar;
    QLabel* m_countLabel;
};

#endif // NOTIFICATIONWINDOW_H
