# Qt通知UI组件

这是一个基于Qt5的通知UI组件，提供了完整的通知管理功能。

## 功能特性

1. **时间线列表显示** - 以时间线的方式显示所有通知
2. **丰富的通知信息** - 每个通知包含项目、标题、内容、状态、时间、通知方式、操作图标
3. **状态分类** - 支持错误、警告、普通三种状态，并用不同图标和颜色区分
4. **操作菜单** - 点击操作图标可显示下拉菜单，支持删除操作
5. **过滤功能** - 可按状态过滤显示通知
6. **实时更新** - 时间显示会自动更新（如"5分钟前"）

## 组件结构

### 核心类

- **NotificationItem** - 通知数据模型
- **NotificationItemWidget** - 单个通知项UI组件
- **NotificationListWidget** - 通知列表容器组件
- **NotificationWindow** - 主通知窗口

### 文件结构

```
src/componets/notification/
├── NotificationItem.h/cpp          # 通知数据模型
├── NotificationItemWidget.h/cpp    # 通知项UI组件
├── NotificationListWidget.h/cpp    # 通知列表组件
├── NotificationWindow.h/cpp        # 主通知窗口
├── prd.md                          # 产品需求文档
└── README.md                       # 说明文档
```

## 使用方法

### 基本使用

```cpp
#include "NotificationWindow.h"

// 创建通知窗口
NotificationWindow window;
window.show();

// 添加通知
NotificationItem item("项目名", "标题", "内容", 
                     NotificationStatus::Normal, 
                     NotificationType::Silent);
window.addNotification(item);
```

### 通知状态

```cpp
enum class NotificationStatus {
    Normal,     // 普通 - 绿色边框，信息图标
    Warning,    // 警告 - 橙色边框，警告图标
    Error       // 错误 - 红色边框，错误图标
};
```

### 通知方式

```cpp
enum class NotificationType {
    Silent,     // 无弹窗
    Balloon,    // 气球
    Sticky      // 粘性气球
};
```

## UI设计

### 通知项布局

```
[状态图标] [项目标签] [标题]                    [时间] [操作按钮]
           [内容文本（可换行）]
```

### 视觉特性

- 左侧彩色边框表示通知状态
- 悬停效果增强交互体验
- 响应式布局适应不同窗口大小
- 滚动支持处理大量通知

## 构建说明

确保CMakeLists.txt包含了所有通知组件文件：

```cmake
set(NOTIFICATION_SOURCES
    src/componets/notification/NotificationItem.cpp
    src/componets/notification/NotificationItemWidget.cpp
    src/componets/notification/NotificationListWidget.cpp
    src/componets/notification/NotificationWindow.cpp
)
```

## 依赖要求

- Qt5 Core
- Qt5 Gui  
- Qt5 Widgets
- C++17 标准

## 扩展功能

组件设计支持以下扩展：

1. **持久化存储** - 可添加数据库支持保存通知历史
2. **网络通知** - 可集成网络服务接收远程通知
3. **自定义主题** - 可扩展样式系统支持多主题
4. **通知分组** - 可按项目或类型分组显示
5. **搜索功能** - 可添加搜索框快速查找通知

## 测试

运行程序后会自动加载一些测试通知，您可以：

1. 使用过滤下拉框测试状态过滤功能
2. 点击操作按钮测试删除功能
3. 点击"添加测试通知"按钮添加随机通知
4. 点击"清空所有"按钮清空所有通知
